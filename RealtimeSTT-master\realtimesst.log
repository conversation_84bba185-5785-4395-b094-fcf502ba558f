2025-08-19 22:43:45.811 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 22:43:45.821 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 22:43:45.827 - RealTimeSTT: realtimestt - ERROR - Wakeword engine  unknown/unsupported or wake_words not specified. Please specify one of: pvporcupine, openwakeword.
NoneType: None
2025-08-19 22:43:45.829 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 22:43:45.829 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 22:43:46.326 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 22:43:46.329 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 22:43:46.329 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 22:43:55.098 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:43:55.098 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:43:55.101 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:43:55.149 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 22:44:25.837 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 22:44:25.850 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 22:44:26.454 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 22:44:26.455 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 22:44:26.455 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 22:44:34.284 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:44:34.287 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:44:34.322 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 22:44:38.542 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 22:44:43.411 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:44:44.882 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:44:44.883 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 37888
2025-08-19 22:44:44.883 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:44:44.979 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 22:44:44.979 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:44:44.980 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 22:44:44.982 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:44:44.983 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:44.984 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:44:45.080 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.191 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.309 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.408 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.519 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.634 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.739 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.845 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.931 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 22:44:46.017 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.04 seconds
2025-08-19 22:45:01.651 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:45:01.652 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:45:01.652 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:45:01.654 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:45:03.891 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:45:03.893 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 50176
2025-08-19 22:45:03.893 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:45:03.980 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 22:45:03.981 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:45:03.981 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 22:45:03.983 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:45:03.984 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:03.985 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:45:04.092 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.215 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.330 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.440 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.545 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.676 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.824 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.927 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.044 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.141 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.209 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 22:45:05.430 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.45 seconds
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:45:35.826 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:45:45.622 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:45:45.625 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 171008
2025-08-19 22:45:45.625 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 22:45:45.717 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:45:45.718 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:45:45.721 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:45.835 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:45.955 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.069 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.188 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.307 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.430 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.536 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.649 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.751 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.868 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.966 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 22:45:47.008 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.29 seconds
2025-08-19 22:46:09.421 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:46:09.422 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:46:09.422 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:46:09.424 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:46:10.771 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:46:10.772 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 35840
2025-08-19 22:46:10.772 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 22:46:10.842 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:46:10.844 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:46:10.844 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:10.947 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.067 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.172 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.272 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.385 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.501 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.613 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.720 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.741 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 22:46:11.787 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 0.94 seconds
2025-08-19 22:46:25.896 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 22:46:25.897 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 22:46:25.919 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 22:46:26.475 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 22:46:26.540 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 22:46:26.604 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:03:55.089 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:03:55.102 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:03:55.830 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:03:55.831 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:03:55.831 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:04:02.511 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:04:02.513 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:02.517 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:04:07.638 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:04:08.668 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:04:10.138 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:04:10.138 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 37888
2025-08-19 23:04:10.139 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:10.154 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.258 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.379 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.491 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.611 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.721 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.832 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.942 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:11.020 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:04:11.021 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 0.88 seconds
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:04:38.358 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:04:38.359 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 76800
2025-08-19 23:04:38.360 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:04:38.360 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:38.381 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.487 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.596 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.707 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.813 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.929 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.043 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.160 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.270 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.366 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:04:39.368 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.01 seconds
2025-08-19 23:07:47.700 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:07:47.701 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:07:47.721 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:07:48.256 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:07:48.294 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:07:48.359 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:08:00.319 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:08:00.327 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:08:00.673 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:08:00.674 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:08:00.675 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:08:07.482 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:08:07.526 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:09:16.896 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:09:35.776 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:09:35.780 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 281600
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:09:35.782 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:09:35.782 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:09:35.783 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:35.883 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.000 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.113 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.225 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.340 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.446 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.567 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.679 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.794 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.895 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.011 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.116 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.160 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:09:37.166 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.38 seconds
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:12:17.767 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:12:33.056 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:12:33.060 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 259072
2025-08-19 23:12:33.060 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:12:33.074 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.187 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.296 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.404 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.516 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.627 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.745 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.864 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.969 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.087 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.187 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.288 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.311 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:12:34.317 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.26 seconds
2025-08-19 23:16:40.920 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:16:40.921 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:16:40.934 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:16:41.000 - RealTimeSTT: realtimestt - DEBUG - Receive from stdout pipe
2025-08-19 23:16:41.001 - RealTimeSTT: realtimestt - INFO - None
2025-08-19 23:16:41.438 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:16:41.536 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:16:41.599 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:16:49.873 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:16:49.881 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:16:49.887 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:16:51.376 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:16:51.378 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:16:51.378 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:16:51.378 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:16:51.708 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:16:51.709 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:16:51.709 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:18:08.001 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:18:08.002 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:18:08.002 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:18:08.002 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:18:08.002 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:18:08.038 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:18:11.938 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:18:13.798 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:18:13.798 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:18:13.798 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:18:13.799 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:18:13.817 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:18:14.198 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want
2025-08-19 23:18:14.327 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 23040
2025-08-19 23:18:14.758 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to turn in the loop
2025-08-19 23:18:15.044 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 34304
2025-08-19 23:18:15.621 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to turn the move to the old implement.
2025-08-19 23:18:16.738 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:18:16.739 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 61440
2025-08-19 23:18:16.739 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:18:16.740 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:18:16.740 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:18:16.741 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:18:16.741 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:18:16.741 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:18:16.741 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:16.839 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:16.976 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.084 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.209 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.345 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.441 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.571 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.686 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.822 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:17.949 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.055 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.160 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.288 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.410 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.514 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.639 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.744 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.862 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:18.963 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.076 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.197 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.306 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.418 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.519 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.620 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.736 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:19.788 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:18:19.790 - RealTimeSTT: realtimestt - DEBUG - Model small completed transcription in 3.05 seconds
2025-08-19 23:18:29.098 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:18:29.098 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:18:29.098 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:18:29.099 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:18:29.106 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:18:29.495 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I
2025-08-19 23:18:29.602 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 22016
2025-08-19 23:18:29.995 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you
2025-08-19 23:18:30.439 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 36352
2025-08-19 23:18:30.847 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to
2025-08-19 23:18:30.962 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 44544
2025-08-19 23:18:31.388 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to remove.
2025-08-19 23:18:31.523 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 52736
2025-08-19 23:18:31.986 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to remove the off.
2025-08-19 23:18:32.124 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 62976
2025-08-19 23:18:32.568 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to remove the old implement.
2025-08-19 23:18:32.712 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 72192
2025-08-19 23:18:33.158 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  I want you to remove the old implementation.
2025-08-19 23:18:33.768 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:18:33.770 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 89088
2025-08-19 23:18:33.770 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:18:33.771 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:18:33.771 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:18:33.771 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:18:33.771 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:18:33.772 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:18:33.772 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:33.887 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.008 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.133 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.245 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.374 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.475 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.588 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.689 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.801 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:34.935 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.049 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.152 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.266 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.366 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.487 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.598 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.716 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.840 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:35.950 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.053 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.164 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.265 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.378 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.487 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.596 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.711 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.813 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:18:36.824 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:18:36.826 - RealTimeSTT: realtimestt - DEBUG - Model small completed transcription in 3.05 seconds
2025-08-19 23:30:00.197 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:30:00.198 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:30:00.217 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:30:00.780 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:30:00.872 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:30:00.982 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:30:12.254 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:30:12.263 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:30:12.268 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:30:13.630 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:30:13.632 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:30:13.632 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:30:13.632 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:30:13.979 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:30:13.981 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:30:13.981 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:30:22.142 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:30:22.142 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:30:22.143 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:30:22.143 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:30:22.143 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:30:22.185 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:31:44.047 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:31:44.048 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:31:44.069 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:31:44.601 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:31:44.714 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:31:44.808 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:32:09.490 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:32:09.503 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:32:09.507 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:32:12.199 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:32:12.201 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:32:12.201 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:32:12.201 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:32:12.565 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:32:12.576 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:32:12.577 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:32:22.151 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:32:22.152 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:32:22.152 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:32:22.152 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:32:22.152 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:32:22.175 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:35:44.677 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:35:44.678 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:35:44.698 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:35:44.720 - RealTimeSTT: realtimestt - DEBUG - Receive from stdout pipe
2025-08-19 23:35:44.720 - RealTimeSTT: realtimestt - INFO - None
2025-08-19 23:35:45.271 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:35:45.393 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:35:45.489 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:36:11.038 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:36:11.050 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:36:11.054 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:36:12.410 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:36:12.411 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:36:12.411 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:36:12.411 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:36:12.747 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:36:12.748 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:36:12.748 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:36:18.274 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:36:18.275 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:36:18.275 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:36:18.275 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:36:18.275 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:36:18.296 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:36:37.437 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:36:38.967 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:36:38.967 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:36:38.967 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:36:38.967 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:36:38.979 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:36:39.366 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Go.
2025-08-19 23:36:39.491 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 23040
2025-08-19 23:36:39.895 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The virus.
2025-08-19 23:36:40.041 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 31232
2025-08-19 23:36:40.502 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The file is in seem to
2025-08-19 23:36:40.697 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 42496
2025-08-19 23:36:41.214 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The file doesn't seem to be e.
2025-08-19 23:36:41.364 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 52736
2025-08-19 23:36:41.926 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The file isn't seem to be executing well.
2025-08-19 23:36:42.427 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 70144
2025-08-19 23:36:42.926 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The file doesn't seem to be executing well.
2025-08-19 23:36:44.157 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:36:44.158 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 97280
2025-08-19 23:36:44.158 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:36:44.159 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:36:44.159 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:36:44.159 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:36:44.159 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:36:44.159 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:36:44.176 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.276 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.401 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.530 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.632 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.733 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.835 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:44.934 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:45.036 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:36:45.133 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:36:45.135 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 0.98 seconds
2025-08-19 23:50:45.099 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:50:45.109 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:50:45.115 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:50:46.596 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:50:46.598 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:50:46.598 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:50:46.598 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:50:47.299 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:50:47.306 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:50:47.306 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:50:52.677 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:50:52.677 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:50:52.678 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:50:52.678 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:50:52.678 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:50:52.698 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:50:56.407 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:50:59.197 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:50:59.197 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:50:59.197 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:50:59.198 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:50:59.198 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 15872
2025-08-19 23:50:59.227 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:50:59.227 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:50:59.227 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:50:59.228 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:50:59.240 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:50:59.806 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Just.
2025-08-19 23:50:59.874 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Jerk.
2025-08-19 23:51:01.047 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:51:01.048 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 44032
2025-08-19 23:51:01.048 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:51:01.049 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:51:01.049 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:51:01.049 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:51:01.049 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:51:01.049 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:51:01.060 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.077 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:51:01.078 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 44032
2025-08-19 23:51:01.078 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:51:01.079 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:51:01.079 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:51:01.079 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:51:01.079 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:51:01.079 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:51:01.100 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.177 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.220 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.280 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.347 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.403 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.460 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.522 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.561 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.625 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.672 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.743 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.790 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.856 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.906 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:01.971 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.014 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.087 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.117 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.199 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.234 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.301 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.341 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.401 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.443 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:02.459 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:51:02.460 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.41 seconds
2025-08-19 23:51:02.493 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:51:02.494 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.42 seconds
2025-08-19 23:51:10.777 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:51:10.777 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:51:10.777 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:51:10.777 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:51:10.793 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:51:10.807 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:51:10.807 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:51:10.807 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:51:10.807 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:51:10.827 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:51:11.385 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Done.
2025-08-19 23:51:11.448 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  And then...
2025-08-19 23:51:11.521 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 26112
2025-08-19 23:51:11.597 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 27136
2025-08-19 23:51:12.226 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer type.
2025-08-19 23:51:12.280 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer type.
2025-08-19 23:51:12.411 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 40448
2025-08-19 23:51:12.412 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 40448
2025-08-19 23:51:13.186 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from this.
2025-08-19 23:51:13.236 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from this.
2025-08-19 23:51:13.361 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 54784
2025-08-19 23:51:13.448 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 56832
2025-08-19 23:51:14.261 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved mode.
2025-08-19 23:51:14.343 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved mode up.
2025-08-19 23:51:14.967 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 81408
2025-08-19 23:51:15.007 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 82432
2025-08-19 23:51:15.794 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from the saved mode is...
2025-08-19 23:51:15.860 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from the saved model is buff.
2025-08-19 23:51:15.909 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 95744
2025-08-19 23:51:15.987 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 97792
2025-08-19 23:51:16.781 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from the saved model is about the target device.
2025-08-19 23:51:16.841 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from the saved model is about the target device.
2025-08-19 23:51:16.980 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 113152
2025-08-19 23:51:17.065 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 115200
2025-08-19 23:51:18.189 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type infert from the saved model is about the target device or about end-to-net.
2025-08-19 23:51:18.206 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved model is about the target device or about end-to-ness.
2025-08-19 23:51:18.376 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 135680
2025-08-19 23:51:18.451 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 136704
2025-08-19 23:51:19.668 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved model is about the target device or about end-to-nose support efficient flow.
2025-08-19 23:51:19.683 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved model is about the target device or about end-to-nose support efficient flow.
2025-08-19 23:51:20.917 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:51:20.920 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 176128
2025-08-19 23:51:20.920 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:51:20.921 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:51:20.921 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:51:20.921 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:51:20.921 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:51:20.921 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:51:20.927 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:20.957 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:51:20.959 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 177152
2025-08-19 23:51:20.960 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:51:20.960 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:51:20.961 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:51:20.961 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:51:20.961 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:51:20.961 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:51:20.962 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.032 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.069 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.134 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.184 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.245 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.316 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.371 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.432 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.513 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.543 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.609 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.648 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.721 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.756 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.836 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.879 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.952 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:21.998 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.085 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.124 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.193 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.234 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.316 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.356 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.435 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.475 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.536 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.577 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.637 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.680 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.750 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.788 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.872 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.889 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:22.980 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:23.005 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:23.089 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:51:23.093 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 2.13 seconds
2025-08-19 23:51:23.094 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:51:23.103 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:51:23.106 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 2.18 seconds
2025-08-19 23:51:30.581 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:51:30.582 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:51:30.599 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:51:31.112 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:51:31.160 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:51:31.245 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:51:49.997 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:51:50.004 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:51:50.009 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:51:51.375 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:51:51.377 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:51:51.377 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:51:51.377 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:51:52.100 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:51:52.102 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:51:52.102 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:51:57.419 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:51:57.419 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:51:57.420 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:51:57.420 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:51:57.420 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:51:57.467 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:52:01.437 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:52:02.328 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:52:02.328 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:52:02.328 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:52:02.328 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:52:02.334 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:52:02.367 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:52:02.367 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:52:02.367 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:52:02.367 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:52:02.372 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:52:02.956 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Thank you
2025-08-19 23:52:02.967 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 25088
2025-08-19 23:52:03.006 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  . .
2025-08-19 23:52:03.135 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 27136
2025-08-19 23:52:03.598 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  the computer typing
2025-08-19 23:52:03.726 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer type info.
2025-08-19 23:52:03.867 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 39424
2025-08-19 23:52:03.950 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 39424
2025-08-19 23:52:04.569 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from
2025-08-19 23:52:04.573 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 50688
2025-08-19 23:52:04.700 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from
2025-08-19 23:52:05.083 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the modern.
2025-08-19 23:52:05.248 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 60928
2025-08-19 23:52:05.278 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 61952
2025-08-19 23:52:06.040 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  the compute type inferred from the model.
2025-08-19 23:52:06.042 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 73216
2025-08-19 23:52:06.047 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the model.
2025-08-19 23:52:06.268 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 77312
2025-08-19 23:52:06.850 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer type inferred from the more than efficient folks.
2025-08-19 23:52:06.855 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 86528
2025-08-19 23:52:07.082 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the more than efficient folk.
2025-08-19 23:52:07.441 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the model efficient for computation.
2025-08-19 23:52:07.647 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:52:07.648 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 99328
2025-08-19 23:52:07.648 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:52:07.649 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:52:07.649 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:52:07.649 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:52:07.649 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:52:07.649 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:52:07.662 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:07.769 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:07.881 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:07.989 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.097 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.223 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.326 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.428 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.545 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.567 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:52:08.569 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 113664
2025-08-19 23:52:08.569 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:52:08.572 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:52:08.572 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:52:08.572 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:52:08.572 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:52:08.572 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:52:08.581 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.661 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.694 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.777 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.805 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.881 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.911 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:08.982 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.030 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.091 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.123 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:52:09.125 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.48 seconds
2025-08-19 23:52:09.135 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.248 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.359 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.460 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.561 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.681 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:09.714 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:52:09.716 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.14 seconds
2025-08-19 23:52:23.767 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:52:23.767 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:52:23.767 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:52:23.768 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:52:23.785 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:52:23.927 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:52:23.927 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:52:23.927 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:52:23.927 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:52:23.928 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:52:24.351 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The...
2025-08-19 23:52:24.352 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 24064
2025-08-19 23:52:24.562 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Done.
2025-08-19 23:52:24.706 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 27136
2025-08-19 23:52:25.024 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  the computer
2025-08-19 23:52:25.025 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 34304
2025-08-19 23:52:25.438 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer type info.
2025-08-19 23:52:25.631 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  the compute type inferred
2025-08-19 23:52:25.631 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 44544
2025-08-19 23:52:25.652 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 41472
2025-08-19 23:52:26.455 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the motive.
2025-08-19 23:52:26.456 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 56832
2025-08-19 23:52:26.509 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the modus.
2025-08-19 23:52:26.731 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 58880
2025-08-19 23:52:27.527 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the model efficient compute.
2025-08-19 23:52:27.645 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the model efficient computation.
2025-08-19 23:52:27.737 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:52:27.738 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 77824
2025-08-19 23:52:27.738 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:52:27.739 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:52:27.739 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:52:27.739 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:52:27.740 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:52:27.739 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:52:27.744 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:27.850 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:27.956 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.073 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.189 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.305 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.406 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.509 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.627 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.667 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:52:28.668 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 90112
2025-08-19 23:52:28.668 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:52:28.670 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:52:28.670 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:52:28.670 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:52:28.670 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:52:28.670 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:52:28.679 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.745 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.790 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.860 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.911 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:28.967 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.025 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.084 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.146 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.188 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.256 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.293 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.372 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.404 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.482 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:52:29.484 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.74 seconds
2025-08-19 23:52:29.493 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.608 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.723 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:52:29.840 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:52:29.842 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.17 seconds
2025-08-19 23:52:32.357 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:52:32.357 - RealTimeSTT: realtimestt - DEBUG - Receive from stdout pipe
2025-08-19 23:52:32.358 - RealTimeSTT: realtimestt - INFO - None
2025-08-19 23:52:32.362 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:52:32.372 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:52:32.877 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:52:32.942 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:52:33.035 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:53:14.840 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:53:14.848 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:53:14.854 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny, default device: cpu, compute type: default, device index: 0, download root: None
2025-08-19 23:53:16.775 - RealTimeSTT: realtimestt - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-08-19 23:53:16.777 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:53:16.777 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:53:16.777 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:53:17.121 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:53:17.123 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:53:17.123 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:53:22.501 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:53:22.502 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:53:22.503 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:53:22.503 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:53:22.503 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:53:22.557 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:53:30.747 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:53:32.027 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:53:32.027 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:53:32.027 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:53:32.028 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:53:32.034 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:53:32.287 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:53:32.287 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:53:32.287 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:53:32.287 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:53:32.290 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 15872
2025-08-19 23:53:32.591 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This.
2025-08-19 23:53:32.737 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 26112
2025-08-19 23:53:32.952 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This is...
2025-08-19 23:53:33.121 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 28160
2025-08-19 23:53:33.636 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide.
2025-08-19 23:53:33.927 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a mic.
2025-08-19 23:53:34.397 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 48128
2025-08-19 23:53:34.397 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 52224
2025-08-19 23:53:35.182 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more.
2025-08-19 23:53:35.221 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more.
2025-08-19 23:53:35.323 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 62976
2025-08-19 23:53:35.411 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 68096
2025-08-19 23:53:36.161 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more responsive experience.
2025-08-19 23:53:36.271 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more responsive experience.
2025-08-19 23:53:36.407 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 80384
2025-08-19 23:53:36.567 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 87552
2025-08-19 23:53:37.683 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more responsive experience similar to Windows.
2025-08-19 23:53:37.827 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should not provide a much more responsive experience similar to Windows.
2025-08-19 23:53:38.560 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 119296
2025-08-19 23:53:38.563 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 115200
2025-08-19 23:53:39.499 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text.
2025-08-19 23:53:39.530 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text.
2025-08-19 23:53:39.753 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 137728
2025-08-19 23:53:39.779 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 134656
2025-08-19 23:53:40.994 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text while maintaining the video.
2025-08-19 23:53:41.032 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text while maintaining the video.
2025-08-19 23:53:41.275 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 162304
2025-08-19 23:53:41.307 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 159232
2025-08-19 23:53:42.810 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text while maintaining the benefits of local processing.
2025-08-19 23:53:42.824 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  This should now provide a much more responsive experience similar to Windows built in speech to text while maintaining the benefits of local processing.
2025-08-19 23:53:44.187 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:53:44.190 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 204800
2025-08-19 23:53:44.190 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:53:44.190 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:53:44.191 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:53:44.193 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.193 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.296 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.296 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.411 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.411 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.519 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.631 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.631 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.754 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.754 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.865 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.865 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:44.982 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.100 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.100 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.218 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.346 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.347 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.447 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.448 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.548 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.650 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.752 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.862 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.862 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:45.963 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.065 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.065 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.170 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.170 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.271 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.271 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.379 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.379 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.502 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.604 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.604 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:53:46.627 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:53:46.631 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 2.44 seconds
2025-08-19 23:53:46.641 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:53:46.645 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 2.45 seconds
2025-08-19 23:54:48.827 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:54:48.827 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:54:48.827 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:54:48.827 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:54:48.827 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:54:48.828 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:54:48.828 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:54:48.828 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:54:48.834 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:54:48.836 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 14848
2025-08-19 23:54:49.468 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The Home
2025-08-19 23:54:49.504 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  Thank you.
2025-08-19 23:54:49.628 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 27136
2025-08-19 23:54:49.676 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 28160
2025-08-19 23:54:50.384 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer typing first.
2025-08-19 23:54:50.453 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The computer typing first.
2025-08-19 23:54:50.600 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 42496
2025-08-19 23:54:50.655 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 43520
2025-08-19 23:54:51.536 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved mode.
2025-08-19 23:54:51.585 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved mode.
2025-08-19 23:54:51.766 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 60928
2025-08-19 23:54:51.813 - RealTimeSTT: realtimestt - DEBUG - Current realtime buffer size: 61952
2025-08-19 23:54:52.993 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved model warning.
2025-08-19 23:54:53.055 - RealTimeSTT: realtimestt - DEBUG - Realtime text detected:  The compute type inferred from the saved model warning.
2025-08-19 23:54:53.367 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:54:53.369 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 87040
2025-08-19 23:54:53.369 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:54:53.371 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:54:53.371 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:54:53.371 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:54:53.372 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:54:53.372 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:54:53.373 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.377 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:54:53.379 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 87040
2025-08-19 23:54:53.379 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:54:53.392 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:54:53.392 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:54:53.392 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:54:53.392 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:54:53.392 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:54:53.393 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.477 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.498 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.588 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.609 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.691 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.724 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.800 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.839 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.909 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:53.965 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.021 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.075 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.146 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.190 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.261 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.292 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.366 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.403 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.488 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.523 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.605 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.625 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.717 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.738 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.836 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.857 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.952 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:54.969 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:55.060 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:55.080 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:54:55.149 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:54:55.151 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.78 seconds
2025-08-19 23:54:55.165 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:54:55.167 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.77 seconds
2025-08-19 23:58:38.207 - RealTimeSTT: realtimestt - DEBUG - Receive from stdout pipe
2025-08-19 23:58:38.207 - RealTimeSTT: realtimestt - INFO - None
