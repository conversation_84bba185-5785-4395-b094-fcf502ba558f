2025-08-19 22:43:45.811 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 22:43:45.821 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 22:43:45.827 - RealTimeSTT: realtimestt - ERROR - Wakeword engine  unknown/unsupported or wake_words not specified. Please specify one of: pvporcupine, openwakeword.
NoneType: None
2025-08-19 22:43:45.829 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 22:43:45.829 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 22:43:46.326 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 22:43:46.329 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 22:43:46.329 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 22:43:55.097 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 22:43:55.098 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:43:55.098 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:43:55.101 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:43:55.149 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 22:44:25.837 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 22:44:25.850 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 22:44:25.932 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 22:44:26.454 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 22:44:26.455 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 22:44:26.455 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 22:44:34.284 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:44:34.285 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:44:34.287 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:44:34.322 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 22:44:38.542 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 22:44:43.411 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 22:44:43.412 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:44:44.882 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:44:44.883 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 37888
2025-08-19 22:44:44.883 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:44:44.979 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 22:44:44.979 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:44:44.980 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 22:44:44.982 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:44:44.983 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:44.984 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:44:45.080 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.191 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.309 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.408 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.519 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.634 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.739 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.845 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:44:45.931 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 22:44:46.017 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.04 seconds
2025-08-19 22:45:01.651 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:45:01.652 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:45:01.652 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:45:01.654 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:45:03.891 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:45:03.893 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 50176
2025-08-19 22:45:03.893 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:45:03.980 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 22:45:03.981 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:45:03.981 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 22:45:03.983 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:45:03.984 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:03.985 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:45:04.092 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.215 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.330 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.440 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.545 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.676 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.824 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:04.927 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.044 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.141 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:05.209 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 22:45:05.430 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.45 seconds
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:45:35.822 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:45:35.826 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:45:45.622 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:45:45.625 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 171008
2025-08-19 22:45:45.625 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:45:45.714 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 22:45:45.717 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:45:45.718 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:45:45.721 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:45.835 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:45.955 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.069 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.188 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.307 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.430 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.536 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.649 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.751 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.868 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:45:46.966 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 22:45:47.008 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.29 seconds
2025-08-19 22:46:09.421 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 22:46:09.422 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 22:46:09.422 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 22:46:09.424 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 22:46:10.771 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 22:46:10.772 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 35840
2025-08-19 22:46:10.772 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 22:46:10.840 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 22:46:10.842 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 22:46:10.844 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 22:46:10.844 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:10.947 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.067 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.172 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.272 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.385 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.501 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.613 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.720 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 22:46:11.741 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 22:46:11.787 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 0.94 seconds
2025-08-19 22:46:25.896 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 22:46:25.897 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 22:46:25.919 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 22:46:26.475 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 22:46:26.540 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 22:46:26.604 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:03:55.089 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:03:55.102 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:03:55.108 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:03:55.830 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:03:55.831 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:03:55.831 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:04:02.511 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:02.512 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:04:02.513 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:02.517 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:04:07.638 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:04:08.668 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:04:08.669 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:04:10.138 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:04:10.138 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 37888
2025-08-19 23:04:10.139 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:04:10.140 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:10.154 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.258 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.379 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.491 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.611 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.721 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.832 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:10.942 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:11.020 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:04:11.021 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 0.88 seconds
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:04:34.458 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:04:38.358 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:04:38.359 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 76800
2025-08-19 23:04:38.360 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:04:38.360 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'transcribing'
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'listening'
2025-08-19 23:04:38.361 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:04:38.381 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.487 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.596 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.707 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.813 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:38.929 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.043 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.160 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.270 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:04:39.366 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'inactive'
2025-08-19 23:04:39.368 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.01 seconds
2025-08-19 23:07:47.700 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:07:47.701 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:07:47.721 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:07:48.256 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:07:48.294 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:07:48.359 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
2025-08-19 23:08:00.319 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-08-19 23:08:00.327 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - DEBUG - Porcupine wake word detection engine initialized successfully
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - INFO - Initializing WebRTC voice with Sensitivity 3
2025-08-19 23:08:00.333 - RealTimeSTT: realtimestt - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-08-19 23:08:00.673 - RealTimeSTT: realtimestt - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-08-19 23:08:00.674 - RealTimeSTT: realtimestt - DEBUG - Starting realtime worker
2025-08-19 23:08:00.675 - RealTimeSTT: realtimestt - DEBUG - Waiting for main transcription model to start
2025-08-19 23:08:07.482 - RealTimeSTT: realtimestt - DEBUG - Main transcription model ready
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - Auto-typing initialized with delay: 0.01s
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - DEBUG - RealtimeSTT initialization completed successfully
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:08:07.483 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:08:07.526 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'wakeword'
2025-08-19 23:09:16.896 - RealTimeSTT: realtimestt - INFO - State changed from 'wakeword' to 'listening'
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'recording'
2025-08-19 23:09:19.076 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:09:35.776 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:09:35.780 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 281600
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:09:35.781 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:09:35.782 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:09:35.782 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:09:35.783 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:35.883 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.000 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.113 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.225 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.340 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.446 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.567 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.679 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.794 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:36.895 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.011 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.116 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:09:37.160 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:09:37.166 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.38 seconds
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - voice activity detected
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - recording started
2025-08-19 23:12:17.766 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'recording'
2025-08-19 23:12:17.767 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording stop
2025-08-19 23:12:33.056 - RealTimeSTT: realtimestt - INFO - recording stopped
2025-08-19 23:12:33.060 - RealTimeSTT: realtimestt - DEBUG - No samples removed, final audio length: 259072
2025-08-19 23:12:33.060 - RealTimeSTT: realtimestt - INFO - State changed from 'recording' to 'inactive'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - Setting listen time
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - State changed from 'inactive' to 'listening'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - DEBUG - Waiting for recording start
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - INFO - State changed from 'listening' to 'transcribing'
2025-08-19 23:12:33.061 - RealTimeSTT: realtimestt - DEBUG - Adding transcription request, no early transcription started
2025-08-19 23:12:33.074 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.187 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.296 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.404 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.516 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.627 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.745 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.864 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:33.969 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.087 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.187 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.288 - RealTimeSTT: realtimestt - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-08-19 23:12:34.311 - RealTimeSTT: realtimestt - INFO - State changed from 'transcribing' to 'inactive'
2025-08-19 23:12:34.317 - RealTimeSTT: realtimestt - DEBUG - Model base completed transcription in 1.26 seconds
2025-08-19 23:16:40.920 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-08-19 23:16:40.921 - RealTimeSTT: realtimestt - DEBUG - Finishing recording thread
2025-08-19 23:16:40.934 - RealTimeSTT: realtimestt - DEBUG - Terminating reader process
2025-08-19 23:16:41.000 - RealTimeSTT: realtimestt - DEBUG - Receive from stdout pipe
2025-08-19 23:16:41.001 - RealTimeSTT: realtimestt - INFO - None
2025-08-19 23:16:41.438 - RealTimeSTT: realtimestt - DEBUG - Terminating transcription process
2025-08-19 23:16:41.536 - RealTimeSTT: realtimestt - DEBUG - Finishing realtime thread
2025-08-19 23:16:41.599 - RealTimeSTT: realtimestt - INFO - KeyboardInterrupt in text() method
