"""
Minimal test version to isolate the startup issue
"""

import os
import sys
import time
import threading

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class MinimalWakeWordTyper:
    def __init__(self):
        self.wake_word_detected = False
        self.typing_active = False
        self.last_realtime_text = ""
        self.typed_text = ""
        self.typing_lock = threading.Lock()
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        print("🎯 Wake word detected!")
        # Reset state for new session
        with self.typing_lock:
            self.last_realtime_text = ""
            self.typed_text = ""
    
    def on_realtime_transcription_update(self, text):
        """Callback for real-time transcription updates"""
        if not text or not text.strip():
            return
        
        print(f"Real-time: {text}")
    
    def process_text(self, text):
        """Process final transcribed text"""
        if text.strip():
            print(f"Final: {text}")
    
    def run(self):
        print("🎯 Minimal Test Version")
        print("Ready - Say 'jarvis' then speak your message")
        print()
        
        try:
            print("🔄 Initializing recorder...")
            
            # Minimal configuration - only essential parameters
            recorder = AudioToTextRecorder(
                # Basic configuration
                model="base",
                language="en",
                
                # Real-time transcription
                enable_realtime_transcription=True,
                realtime_model_type="tiny",
                realtime_processing_pause=0.05,
                on_realtime_transcription_update=self.on_realtime_transcription_update,
                
                # Wake word configuration
                wakeword_backend="pvporcupine",
                wake_words="jarvis",
                wake_words_sensitivity=0.6,
                wake_word_timeout=5,
                wake_word_activation_delay=0,
                
                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                
                # Disable auto-typing for this test
                enable_auto_typing=False,
                
                # Basic settings
                spinner=False,
            )
            
            print("✅ Recorder initialized successfully!")
            print("🚀 Test version ready!")
            print("👂 Listening for 'jarvis'...")
            
            # Main loop
            while True:
                try:
                    text = recorder.text(self.process_text)
                    
                except KeyboardInterrupt:
                    print("\n🛑 Stopping...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("❌ Error: pyautogui is required for auto-typing functionality.")
                print("📦 Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("❌ Error: pvporcupine is required for wake word detection.")
                print("📦 Install it with: pip install pvporcupine")
            else:
                print(f"❌ Import error: {e}")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Failed to initialize recorder: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
            
        finally:
            try:
                recorder.shutdown()
                print("✅ Recorder shut down successfully")
            except:
                pass


def main():
    typer = MinimalWakeWordTyper()
    typer.run()


if __name__ == "__main__":
    main()
