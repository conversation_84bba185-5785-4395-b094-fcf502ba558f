"""
Test script to verify the incremental typing logic works correctly
"""

import re

def clean_realtime_text(text):
    """Clean real-time text to remove unwanted periods from brief pauses"""
    if not text:
        return text

    # Be more conservative with cleaning to avoid text corruption
    # Remove periods that are clearly from pauses
    # Pattern 1: Period followed by space and lowercase letter
    cleaned = re.sub(r'\.(\s+[a-z])', r'\1', text)

    # Pattern 2: Period directly followed by lowercase letter (no space)
    cleaned = re.sub(r'\.([a-z])', r' \1', cleaned)

    # Pattern 3: Remove periods before very common continuation words
    common_continuation_words = ['the', 'and', 'of', 'to', 'a', 'in', 'that', 'it', 'for', 'on', 'with', 'as', 'you', 'at', 'this', 'but', 'from', 'they', 'we', 'or', 'an', 'will', 'my', 'all', 'would', 'there']

    for word in common_continuation_words:
        # With space
        pattern = r'\.(\s+' + word + r'\b)'
        cleaned = re.sub(pattern, r'\1', cleaned, flags=re.IGNORECASE)
        # Without space
        pattern = r'\.(' + word + r'\b)'
        cleaned = re.sub(pattern, r' \1', cleaned, flags=re.IGNORECASE)

    return cleaned

def test_incremental_typing():
    """Test the incremental typing logic"""
    print("Testing incremental typing logic...")
    
    # Simulate real-time transcription updates
    updates = [
        "The",
        "The compute",
        "The compute type",
        "The compute type inferred",
        "The compute type inferred from",
        "The compute type inferred from the",
        "The compute type inferred from the saved",
        "The compute type inferred from the saved model",
        "The compute type inferred from the saved model warning"
    ]
    
    last_realtime_text = ""
    typed_output = ""
    
    print("\nSimulating real-time updates:")
    print("=" * 50)
    
    for i, text in enumerate(updates):
        cleaned_text = clean_realtime_text(text)
        
        # Only process if we have new content and it's longer than what we've typed
        if cleaned_text != last_realtime_text and len(cleaned_text) > len(last_realtime_text):
            # Find the common prefix between old and new text
            common_length = 0
            min_length = min(len(last_realtime_text), len(cleaned_text))
            
            for j in range(min_length):
                if last_realtime_text[j] == cleaned_text[j]:
                    common_length += 1
                else:
                    break
            
            # Only type the truly new part (after the common prefix)
            new_text = cleaned_text[common_length:]
            
            if new_text.strip() or new_text == " ":  # Include spaces
                print(f"Update {i+1}: '{cleaned_text}'")
                print(f"  New text to type: '{new_text}'")
                typed_output += new_text
                print(f"  Total typed so far: '{typed_output}'")
                print()
            
            last_realtime_text = cleaned_text
    
    print("=" * 50)
    print(f"Final typed output: '{typed_output}'")
    print(f"Expected output: '{updates[-1]}'")
    print(f"Match: {typed_output == updates[-1]}")

def test_text_cleaning():
    """Test the text cleaning function"""
    print("\nTesting text cleaning function...")
    print("=" * 50)
    
    test_cases = [
        ("The compute.type inferred", "The compute type inferred"),
        ("I want you to remove.the off.implement.tion", "I want you to remove the off implement tion"),
        ("The sky is blue.", "The sky is blue."),  # Should preserve legitimate periods
        ("Hello.world and.the test", "Hello world and the test"),
        ("This is.a test.of the system", "This is a test of the system"),
    ]
    
    for original, expected in test_cases:
        cleaned = clean_realtime_text(original)
        print(f"Original: '{original}'")
        print(f"Cleaned:  '{cleaned}'")
        print(f"Expected: '{expected}'")
        print(f"Match: {cleaned == expected}")
        print()

if __name__ == "__main__":
    test_text_cleaning()
    test_incremental_typing()
