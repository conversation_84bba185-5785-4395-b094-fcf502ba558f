"""
Enhanced Real-time Wake Word + Auto-typing for RealtimeSTT

This is an optimized version that provides the most responsive real-time transcription
experience possible with minimal delay between speech and text appearance.

Features:
- Ultra-low latency real-time transcription (50ms processing intervals)
- Immediate text appearance as you speak
- Smart text correction and stabilization
- Wake word activation with "jarvis"
- Optimized for responsiveness over perfect accuracy

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say "jarvis" to activate speech recognition
5. Start speaking - text appears immediately as you speak!

Press Ctrl+C to exit.
"""

import os
import sys
import time
import threading

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class RealtimeWakeWordTyper:
    def __init__(self):
        self.wake_word_detected = False
        self.typing_active = False
        self.last_realtime_text = ""
        self.typed_text = ""
        self.typing_lock = threading.Lock()
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        # Reset state for new session
        with self.typing_lock:
            self.last_realtime_text = ""
            self.typed_text = ""

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        pass

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        pass

    def on_recording_stop(self):
        """Callback when recording stops"""
        pass

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True

    def on_typing_complete(self):
        """Callback when auto-typing completes"""
        self.typing_active = False

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error"""
        self.typing_active = False
    
    def on_realtime_transcription_update(self, text):
        """Callback for real-time transcription updates - types immediately"""
        if not text or not text.strip():
            return

        # Clean the text to remove unwanted periods from pauses
        cleaned_text = self._clean_realtime_text(text)

        with self.typing_lock:
            # Only type new content that wasn't typed before
            if cleaned_text != self.last_realtime_text and len(cleaned_text) > len(self.typed_text):
                new_content = cleaned_text[len(self.typed_text):]
                if new_content.strip():
                    try:
                        import pyautogui
                        # Type the new content immediately
                        pyautogui.typewrite(new_content, interval=0.003)  # Very fast typing
                        self.typed_text = cleaned_text
                    except Exception as e:
                        pass  # Silent error handling

            self.last_realtime_text = cleaned_text

    def _clean_realtime_text(self, text):
        """Clean real-time text to remove unwanted periods from brief pauses"""
        if not text:
            return text

        # Remove periods that appear to be from brief pauses rather than sentence endings
        # This is a simple heuristic - remove periods that are followed by lowercase words
        import re

        # Pattern to match periods followed by lowercase words (likely pause artifacts)
        # But preserve periods at the end of text or before uppercase letters
        cleaned = re.sub(r'\.(\s+[a-z])', r'\1', text)

        # Also remove periods that appear in the middle of what seems like a continuous thought
        # Remove periods followed by common continuation words
        continuation_words = ['the', 'and', 'of', 'to', 'a', 'in', 'that', 'have', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their']

        for word in continuation_words:
            pattern = r'\.(\s+' + word + r'\b)'
            cleaned = re.sub(pattern, r'\1', cleaned, flags=re.IGNORECASE)

        return cleaned

    def on_realtime_transcription_stabilized(self, text):
        """Callback for stabilized real-time transcription"""
        # Silent processing - no output
        pass

    def process_text(self, text):
        """Process final transcribed text"""
        if text.strip():
            # Add a space after the final transcription
            try:
                import pyautogui
                pyautogui.typewrite(" ", interval=0.003)
            except:
                pass
    
    def run(self):
        print("🎯 RealtimeSTT Ultra-Responsive Wake Word Auto-Typing")
        print("Ready - Say 'jarvis' then speak your message")
        print()
        
        try:
            # Initialize the recorder with optimized real-time settings
            recorder = AudioToTextRecorder(
                # Model configuration - optimized for speed
                model="small",  # Faster than base, still good accuracy
                language="en",

                # Disable automatic punctuation formatting that causes period insertion
                ensure_sentence_starting_uppercase=False,
                ensure_sentence_ends_with_period=False,

                # Real-time transcription settings - optimized for minimal latency
                enable_realtime_transcription=True,
                realtime_model_type="tiny",  # Fastest model for real-time
                realtime_processing_pause=0.02,  # Process every 20ms for ultra-responsiveness
                on_realtime_transcription_update=self.on_realtime_transcription_update,
                on_realtime_transcription_stabilized=self.on_realtime_transcription_stabilized,

                # Custom prompts to prevent unwanted punctuation
                initial_prompt="Transcribe speech without adding periods during pauses. Only add punctuation at natural sentence endings.",
                initial_prompt_realtime="Transcribe continuously without adding periods for brief pauses. Keep text flowing naturally.",
                
                # Wake word configuration
                wakeword_backend="pvporcupine",
                wake_words="jarvis",
                wake_words_sensitivity=0.6,
                wake_word_timeout=5,
                wake_word_activation_delay=0,
                
                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,
                
                # Disable built-in auto-typing (we handle it manually for better control)
                enable_auto_typing=False,
                
                # Auto-typing callbacks (for final transcription if needed)
                on_auto_typing_start=self.on_typing_start,
                on_auto_typing_complete=self.on_typing_complete,
                on_auto_typing_error=self.on_typing_error,
                
                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,
                
                # Voice activity detection settings - optimized for responsiveness
                silero_sensitivity=0.05,  # More sensitive for faster detection
                webrtc_sensitivity=3,
                post_speech_silence_duration=1.0,  # Shorter silence for faster completion
                min_length_of_recording=0.2,  # Very short minimum for immediate response
                
                # Performance optimizations
                beam_size=3,  # Smaller beam size for faster processing
                beam_size_realtime=1,  # Minimal beam size for real-time
                batch_size=8,  # Smaller batch for faster processing
                realtime_batch_size=4,  # Small real-time batch
                
                # UI settings
                spinner=False,
            )
            
            # Silent initialization - no output
            
            # Main loop
            while True:
                try:
                    text = recorder.text(self.process_text)
                    
                except KeyboardInterrupt:
                    print("\n🛑 Stopping...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("❌ Error: pyautogui is required for auto-typing functionality.")
                print("📦 Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("❌ Error: pvporcupine is required for wake word detection.")
                print("📦 Install it with: pip install pvporcupine")
            else:
                print(f"❌ Import error: {e}")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Failed to initialize recorder: {e}")
            sys.exit(1)
            
        finally:
            try:
                recorder.shutdown()
                print("✅ Recorder shut down successfully")
            except:
                pass


def main():
    typer = RealtimeWakeWordTyper()
    typer.run()


if __name__ == "__main__":
    main()
