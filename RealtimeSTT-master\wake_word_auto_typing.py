"""
Wake Word + Auto-typing example for RealtimeSTT

This example demonstrates wake word activation combined with auto-typing functionality.
The application will:
1. Listen continuously for a wake word (like "jarvis")
2. When the wake word is detected, start recording your speech
3. Transcribe the speech to text using the local Whisper model
4. Automatically type the transcribed text into whatever text field is currently active/focused

Features:
- Wake word activation (no constant recording)
- Local processing only (no API tokens consumed)
- Auto-typing into any active text input field
- Visual feedback and status updates

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say the wake word "jarvis" (or your chosen wake word)
5. Speak your message
6. Watch as your speech is automatically typed into the active text field

Press Ctrl+C to exit.
"""

import os
import sys
import time

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class WakeWordAutoTyper:
    def __init__(self):
        self.wake_word_detected = False
        self.typing_active = False
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        print("🎯 Wake word detected! Listening...")

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        # Reduced verbosity - no output for timeout
        pass

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        # Reduced verbosity - no output for detection start
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        print("🎤 Recording...")

    def on_recording_stop(self):
        """Callback when recording stops"""
        print("⏹️  Processing...")

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True
        # Reduced verbosity - no output for typing start
        pass

    def on_typing_complete(self):
        """Callback when auto-typing completes"""
        self.typing_active = False
        print("✅ Typed successfully")

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error"""
        self.typing_active = False
        print(f"❌ Typing error: {error}")
    
    def process_text(self, text):
        """Process transcribed text"""
        if text.strip():
            print(f"📝 '{text}'")
            # Auto-typing happens automatically in the AudioToTextRecorder
    
    def run(self):
        print("🎯 RealtimeSTT Wake Word Auto-Typing")
        print("Ready - Say 'jarvis' then speak your message")
        print("Press Ctrl+C to exit")
        print()
        
        try:
            # Initialize the recorder with wake word + auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="base",  # Good balance of speed and accuracy
                language="en",  # Set to your preferred language
                
                # Wake word configuration
                wakeword_backend="pvporcupine",  # Use Porcupine backend
                wake_words="jarvis",  # You can change this to other supported wake words
                wake_words_sensitivity=0.6,  # Adjust sensitivity (0.0-1.0)
                wake_word_timeout=5,  # Timeout after wake word detection
                wake_word_activation_delay=0,  # Immediate activation
                
                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,
                
                # Auto-typing configuration
                enable_auto_typing=True,
                auto_typing_delay=0.01,  # Small delay between keystrokes
                auto_typing_fail_safe=True,  # Enable fail-safe
                auto_typing_add_space=True,  # Add space after each transcription
                
                # Auto-typing callbacks
                on_auto_typing_start=self.on_typing_start,
                on_auto_typing_complete=self.on_typing_complete,
                on_auto_typing_error=self.on_typing_error,
                
                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,
                
                # Voice activity detection settings
                silero_sensitivity=0.1,  # Adjust for your environment
                webrtc_sensitivity=3,
                post_speech_silence_duration=0.8,  # Wait after speech ends
                min_length_of_recording=0.5,  # Minimum recording length
                
                # UI settings
                spinner=False,  # Disable spinner for clean output
            )
            
            print("🚀 Initialized - Listening for 'jarvis'...")
            
            # Main loop - continuously listen for wake word and process speech
            while True:
                try:
                    # This will wait for wake word, then record and auto-type
                    text = recorder.text(self.process_text)
                    
                except KeyboardInterrupt:
                    print("\n🛑 Stopping...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("❌ Error: pyautogui is required for auto-typing functionality.")
                print("📦 Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("❌ Error: pvporcupine is required for wake word detection.")
                print("📦 Install it with: pip install pvporcupine")
            else:
                print(f"❌ Import error: {e}")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Failed to initialize recorder: {e}")
            sys.exit(1)
            
        finally:
            try:
                recorder.shutdown()
                print("✅ Recorder shut down successfully")
            except:
                pass


def main():
    typer = WakeWordAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
