"""
Wake Word + Auto-typing example for RealtimeSTT

This example demonstrates wake word activation combined with auto-typing functionality.
The application will:
1. Listen continuously for a wake word (like "jarvis")
2. When the wake word is detected, start recording your speech
3. Transcribe the speech to text using the local Whisper model
4. Automatically type the transcribed text into whatever text field is currently active/focused

Features:
- Wake word activation (no constant recording)
- Local processing only (no API tokens consumed)
- Auto-typing into any active text input field
- Visual feedback and status updates

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say the wake word "jarvis" (or your chosen wake word)
5. Speak your message
6. Watch as your speech is automatically typed into the active text field

Press Ctrl+C to exit.
"""

import os
import sys
import time

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder


class WakeWordAutoTyper:
    def __init__(self):
        self.wake_word_detected = False
        self.typing_active = False
        self.last_realtime_text = ""
        self.current_typed_length = 0
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        print("🎯 Wake word detected! Listening...")

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        # Reduced verbosity - no output for timeout
        pass

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        # Reduced verbosity - no output for detection start
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        print("🎤 Recording...")

    def on_recording_stop(self):
        """Callback when recording stops"""
        print("⏹️  Processing...")

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True
        # Reduced verbosity - no output for typing start
        pass

    def on_typing_complete(self):
        """Callback when auto-typing completes"""
        self.typing_active = False
        print("✅ Final transcription completed")
        # Reset for next wake word detection
        self.last_realtime_text = ""
        self.current_typed_length = 0

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error"""
        self.typing_active = False
        print(f"❌ Typing error: {error}")
        # Reset for next wake word detection
        self.last_realtime_text = ""
        self.current_typed_length = 0

    def _clean_realtime_text(self, text):
        """Clean real-time text to remove unwanted periods from brief pauses"""
        if not text:
            return text

        # Be more conservative with cleaning to avoid text corruption
        import re

        # Remove periods that are clearly from pauses
        # Pattern 1: Period followed by space and lowercase letter
        cleaned = re.sub(r'\.(\s+[a-z])', r'\1', text)

        # Pattern 2: Period directly followed by lowercase letter (no space)
        cleaned = re.sub(r'\.([a-z])', r' \1', cleaned)

        # Pattern 3: Remove periods before very common continuation words
        common_continuation_words = ['the', 'and', 'of', 'to', 'a', 'in', 'that', 'it', 'for', 'on', 'with', 'as', 'you', 'at', 'this', 'but', 'from', 'they', 'we', 'or', 'an', 'will', 'my', 'all', 'would', 'there']

        for word in common_continuation_words:
            # With space
            pattern = r'\.(\s+' + word + r'\b)'
            cleaned = re.sub(pattern, r'\1', cleaned, flags=re.IGNORECASE)
            # Without space
            pattern = r'\.(' + word + r'\b)'
            cleaned = re.sub(pattern, r' \1', cleaned, flags=re.IGNORECASE)

        return cleaned

    def on_realtime_transcription_update(self, text):
        """Callback for real-time transcription updates"""
        if not text or not text.strip():
            return

        # Clean the text to remove unwanted periods from pauses
        cleaned_text = self._clean_realtime_text(text)

        # Only process if we have new content and it's longer than what we've typed
        if cleaned_text != self.last_realtime_text and len(cleaned_text) > len(self.last_realtime_text):
            # Find the common prefix between old and new text
            common_length = 0
            min_length = min(len(self.last_realtime_text), len(cleaned_text))

            for i in range(min_length):
                if self.last_realtime_text[i] == cleaned_text[i]:
                    common_length += 1
                else:
                    break

            # Only type the truly new part (after the common prefix)
            new_text = cleaned_text[common_length:]

            if new_text.strip():
                print(f"📝 Real-time: '{cleaned_text}'")
                # Type only the new portion
                try:
                    import pyautogui
                    pyautogui.typewrite(new_text, interval=0.005)  # Fast typing
                except Exception as e:
                    print(f"❌ Real-time typing error: {e}")

            self.last_realtime_text = cleaned_text

    def on_realtime_transcription_stabilized(self, text):
        """Callback for stabilized real-time transcription"""
        if text and text.strip():
            print(f"📝 Stabilized: '{text}'")
            # The stabilized text is more accurate, but we've already typed most of it
            # We could implement correction logic here if needed

    def process_text(self, text):
        """Process final transcribed text"""
        if text.strip():
            print(f"📝 Final: '{text}'")
            # Reset counters for next session
            self.last_realtime_text = ""
            self.current_typed_length = 0
            # Final text auto-typing is handled by the AudioToTextRecorder
    
    def run(self):
        print("🎯 RealtimeSTT Real-time Wake Word Auto-Typing")
        print("✨ Real-time transcription enabled - text appears as you speak!")
        print("Ready - Say 'jarvis' then speak your message")
        print("Press Ctrl+C to exit")
        print()
        
        try:
            # Initialize the recorder with wake word + real-time auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="base",  # Main model for final transcription
                language="en",  # Set to your preferred language

                # Disable automatic punctuation formatting that causes period insertion
                ensure_sentence_starting_uppercase=False,
                ensure_sentence_ends_with_period=False,

                # Real-time transcription settings
                enable_realtime_transcription=True,
                realtime_model_type="tiny",  # Fast model for real-time processing
                realtime_processing_pause=0.05,  # Process every 50ms for responsiveness
                on_realtime_transcription_update=self.on_realtime_transcription_update,
                on_realtime_transcription_stabilized=self.on_realtime_transcription_stabilized,

                # Custom prompts to prevent unwanted punctuation
                initial_prompt="Transcribe speech without adding periods during pauses. Only add punctuation at natural sentence endings.",
                initial_prompt_realtime="Transcribe continuously without adding periods for brief pauses. Keep text flowing naturally.",

                # Wake word configuration
                wakeword_backend="pvporcupine",  # Use Porcupine backend
                wake_words="jarvis",  # You can change this to other supported wake words
                wake_words_sensitivity=0.6,  # Adjust sensitivity (0.0-1.0)
                wake_word_timeout=5,  # Timeout after wake word detection
                wake_word_activation_delay=0,  # Immediate activation

                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,

                # Auto-typing configuration (for final transcription)
                enable_auto_typing=False,  # Disable built-in auto-typing (we handle it manually for real-time)
                auto_typing_delay=0.005,  # Fast typing for final corrections
                auto_typing_fail_safe=True,  # Enable fail-safe
                auto_typing_add_space=True,  # Add space after each transcription

                # Auto-typing callbacks
                on_auto_typing_start=self.on_typing_start,
                on_auto_typing_complete=self.on_typing_complete,
                on_auto_typing_error=self.on_typing_error,

                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,

                # Voice activity detection settings
                silero_sensitivity=0.1,  # Adjust for your environment
                webrtc_sensitivity=3,
                post_speech_silence_duration=1.5,  # Reduced for faster response
                min_length_of_recording=0.3,  # Shorter minimum for responsiveness

                # UI settings
                spinner=False,  # Disable spinner for clean output
            )
            
            print("🚀 Real-time transcription ready - Listening for 'jarvis'...")
            
            # Main loop - continuously listen for wake word and process speech
            while True:
                try:
                    # This will wait for wake word, then record and auto-type
                    text = recorder.text(self.process_text)
                    
                except KeyboardInterrupt:
                    print("\n🛑 Stopping...")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("❌ Error: pyautogui is required for auto-typing functionality.")
                print("📦 Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("❌ Error: pvporcupine is required for wake word detection.")
                print("📦 Install it with: pip install pvporcupine")
            else:
                print(f"❌ Import error: {e}")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Failed to initialize recorder: {e}")
            sys.exit(1)
            
        finally:
            try:
                recorder.shutdown()
                print("✅ Recorder shut down successfully")
            except:
                pass


def main():
    typer = WakeWordAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
